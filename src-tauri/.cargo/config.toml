[build]
# 使用更多CPU核心进行并行编译
jobs = 8

# 启用共享泛型单态化，减少编译时间
# rustflags = ["-C", "target-cpu=native"]  # 注释掉以支持交叉编译

[target.x86_64-apple-darwin]
# macOS特定优化
rustflags = ["-C", "link-arg=-Wl,-ld_classic", "-C", "target-cpu=native"]

[target.x86_64-pc-windows-gnu]
# Windows交叉编译配置 (使用GNU工具链)
linker = "x86_64-w64-mingw32-gcc"
rustflags = [
    "-C", "target-feature=+crt-static",
    "-C", "target-cpu=x86-64"
]

[target.x86_64-pc-windows-msvc]
# Windows交叉编译配置 (MSVC工具链，需要Visual Studio) - 暂时禁用
# rustflags = [
#     "-C", "target-feature=+crt-static",
#     "-C", "target-cpu=x86-64"
# ]

# 缓存配置
[net]
retry = 2
git-fetch-with-cli = true

# 注册表配置
[registries.crates-io]
protocol = "sparse"

# 编译缓存和优化
[env]
CARGO_INCREMENTAL = "1"
RUST_LOG = "goldfish_app=info,warn,error"  # 只显示应用自身的重要日志，过滤第三方库的调试日志
# 如果安装了sccache，启用编译缓存
# RUSTC_WRAPPER = "sccache"
