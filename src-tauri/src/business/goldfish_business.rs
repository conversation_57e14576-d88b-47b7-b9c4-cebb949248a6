use crate::models::config::MonitorConfig;
use crate::services::{
    account::{AccountService, WindowsAccountService},
    auth::AuthService,
    config::ConfigService,
    monitor::MonitorService,
    notification::NotificationService,
};
use crate::utils::{WindowConfig, WindowFactory, WindowParams};
use log::info;
use tauri::AppHandle;

/// 闲鱼业务逻辑处理器 - 协调各个服务完成业务功能
#[derive(Clone)]
pub struct GoldfishBusiness {
    app_handle: AppHandle,
    config_service: ConfigService,
    auth_service: AuthService,
    monitor_service: MonitorService,
    notification_service: NotificationService,
    account_service: AccountService,
}

impl GoldfishBusiness {
    /// 创建新的业务处理器
    pub fn new(
        app_handle: AppHandle,
        config_service: ConfigService,
        auth_service: AuthService,
        monitor_service: MonitorService,
        notification_service: NotificationService,
        account_service: AccountService,
    ) -> Self {
        Self {
            app_handle,
            config_service,
            auth_service,
            monitor_service,
            notification_service,
            account_service,
        }
    }

    /// 启动闲鱼监控业务流程
    pub async fn start_goldfish_monitoring(&self) -> Result<(), String> {
        // 1. 记录业务日志
        info!("GoldfishBusiness::start_goldfish_monitoring - 开始启动闲鱼监控");

        // 2. 检查激活状态
        if !self.auth_service.check_activation_status().await? {
            return Err("应用未激活，无法启动监控".to_string());
        }

        // 3. 验证所有启用账号的Cookie有效性
        info!("GoldfishBusiness::start_goldfish_monitoring - 开始验证账号Cookie有效性");

        if let Err(e) = self.account_service.validate_all_accounts().await {
            return Err(format!("账号验证失败: {}", e));
        }

        // 4. 获取配置
        let config = self.config_service.get_config().await;

        // 5. 设置通知渠道
        self.setup_notification_channels(&config).await?;

        // 6. 启动监控
        self.monitor_service.start_monitoring(config).await?;

        // 7. 记录成功日志
        info!("GoldfishBusiness::start_goldfish_monitoring - 闲鱼监控启动成功");

        Ok(())
    }

    /// 停止闲鱼监控
    pub async fn stop_goldfish_monitoring(&self) -> Result<(), String> {
        info!("GoldfishBusiness::stop_goldfish_monitoring - 停止闲鱼监控");

        self.monitor_service.stop_monitoring().await?;

        Ok(())
    }

    /// 处理闲鱼登录流程
    pub async fn handle_goldfish_login(&self) -> Result<(), String> {
        info!("GoldfishBusiness::handle_goldfish_login - 开始闲鱼登录流程");

        // 1. 使用新的窗口工厂创建登录窗口
        let login_url = "https://www.goofish.com/search?q=相机";

        let self_clone = self.clone();
        let params = crate::utils::WindowParams::new()
            .with_title("闲鱼登录 - 请完成登录后关闭窗口")
            .with_config(crate::utils::WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let window = window.clone();

                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 登录窗口关闭请求，开始处理Cookie提取");

                        // 同步提取Cookie，避免竞态条件（统一处理逻辑）
                        let cookies_result = window.cookies();
                        match cookies_result {
                            Ok(cookies) => {
                                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                                // 保存所有Cookie
                                let all_cookies: Vec<_> = cookies
                                    .into_iter()
                                    .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                    .collect();

                                if !all_cookies.is_empty() {
                                    let cookie_string = all_cookies.join("; ");
                                    println!("🔄 开始处理登录完成");

                                    // 异步处理登录完成，但不阻止窗口关闭
                                    let self_clone_async = self_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        if let Err(e) = self_clone_async
                                            .handle_login_window_close_with_cookie(cookie_string)
                                            .await
                                        {
                                            println!("❌ 处理登录完成失败: {}", e);
                                        }
                                    });
                                } else {
                                    println!("⚠️ 未找到任何Cookie");
                                }
                            }
                            Err(e) => {
                                println!("⚠️ Cookie提取失败: {}", e);
                            }
                        }
                    }
                    _ => {}
                }
            });

        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            "goofish_login",
            login_url,
            Some(params),
            None,
        )?;

        info!(
            "GoldfishBusiness::handle_goldfish_login - 已打开登录窗口: {}",
            window.label()
        );

        // 窗口事件处理现在直接在窗口创建时处理，无需延迟注册回调

        Ok(())
    }

    /// 处理标准登录窗口的Cookie（新方法）
    async fn handle_login_window_close_with_cookie(
        &self,
        cookie_string: String,
    ) -> Result<(), String> {
        println!("🔄 开始验证Cookie并自动添加账号");

        // 验证Cookie并获取用户信息
        match self.account_service.get_user_info(&cookie_string).await {
            Ok(user_info) if user_info.is_valid => {
                println!("✅ Cookie验证成功，自动添加账号");

                // 添加账号
                match self
                    .account_service
                    .add_account_with_user_info(
                        user_info
                            .nickname
                            .clone()
                            .unwrap_or_else(|| "未知用户".to_string()),
                        user_info.user_id.clone(),
                        cookie_string,
                        Some("通过登录窗口自动添加".to_string()),
                    )
                    .await
                {
                    Ok((account, is_update)) => {
                        let message = if is_update {
                            format!("账号 {} 信息已更新", account.name)
                        } else {
                            format!("账号 {} 已添加", account.name)
                        };
                        println!("✅ {}", message);
                    }
                    Err(e) => {
                        println!("⚠️ 账号添加失败: {}", e);
                    }
                }
            }
            Ok(_) => {
                println!("⚠️ Cookie验证失败");
            }
            Err(e) => {
                println!("⚠️ Cookie验证出错: {}", e);
            }
        }

        Ok(())
    }

    /// 处理登录窗口关闭事件（保留原方法以兼容性）
    async fn handle_login_window_close(&self, window: &tauri::WebviewWindow) -> Result<(), String> {
        println!("🔄 处理登录窗口关闭，开始提取Cookie");

        // 直接获取 Cookie
        let cookies_result = window.cookies();
        match cookies_result {
            Ok(cookies) => {
                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                // 保存所有Cookie
                let all_cookies: Vec<_> = cookies
                    .into_iter()
                    .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                    .collect();

                if !all_cookies.is_empty() {
                    let cookie_string = all_cookies.join("; ");
                    println!("🔄 开始验证Cookie并自动添加账号");

                    // 验证Cookie并获取用户信息
                    match self.account_service.get_user_info(&cookie_string).await {
                        Ok(user_info) if user_info.is_valid => {
                            println!("✅ Cookie验证成功，自动添加账号");

                            // 添加账号
                            match self
                                .account_service
                                .add_account_with_user_info(
                                    user_info
                                        .nickname
                                        .clone()
                                        .unwrap_or_else(|| "未知用户".to_string()),
                                    user_info.user_id.clone(),
                                    cookie_string,
                                    Some("通过登录窗口自动添加".to_string()),
                                )
                                .await
                            {
                                Ok((account, is_update)) => {
                                    let message = if is_update {
                                        format!("账号 {} 已更新", account.name)
                                    } else {
                                        format!("账号 {} 已添加", account.name)
                                    };
                                    println!("✅ 自动添加账号成功: {}", message);
                                }
                                Err(e) => {
                                    println!("❌ 自动添加账号失败: {}", e);
                                }
                            }
                        }
                        Ok(_) => {
                            println!("❌ Cookie验证失败");
                        }
                        Err(e) => {
                            println!("❌ Cookie验证出错: {}", e);
                        }
                    }
                } else {
                    println!("⚠️ 未找到任何Cookie");
                }
            }
            Err(e) => {
                println!("⚠️ Cookie提取失败: {}", e);
            }
        }

        Ok(())
    }

    /// 设置通知渠道
    async fn setup_notification_channels(&self, config: &MonitorConfig) -> Result<(), String> {
        // 设置钉钉通知渠道（仅在开关开启时）
        if config.dingtalk_enabled {
            for (index, hook_url) in config.dingtalk_hooks.iter().enumerate() {
                let channel_name = format!("dingtalk_{}", index);
                let channel = crate::services::notification::NotificationChannel::DingTalk {
                    webhook_url: hook_url.clone(),
                };

                self.notification_service
                    .add_channel(channel_name, channel)
                    .await?;
            }
        }

        // 添加系统通知渠道
        let system_channel = crate::services::notification::NotificationChannel::System;
        self.notification_service
            .add_channel("system".to_string(), system_channel)
            .await?;

        Ok(())
    }

    /// 发送新商品通知
    pub async fn send_new_item_notification(
        &self,
        item: &crate::models::config::MonitorItem,
    ) -> Result<(), String> {
        let message = crate::services::notification::NotificationMessage {
            title: "发现新商品".to_string(),
            content: format!(
                "商品: {}\n价格: {}\n卖家: {}",
                item.title, item.price, item.user_nick_name
            ),
            level: crate::services::notification::service::NotificationLevel::Info,
            timestamp: chrono::Utc::now().to_rfc3339(),
            metadata: std::collections::HashMap::new(),
        };

        self.notification_service
            .broadcast_notification(message)
            .await?;

        Ok(())
    }

    /// 获取业务状态
    pub async fn get_business_status(&self) -> Result<serde_json::Value, String> {
        let monitor_status = self.monitor_service.get_status().await;
        let activation_info = self.auth_service.get_activation_info().await;
        let config = self.config_service.get_config().await;

        Ok(serde_json::json!({
            "monitor": monitor_status,
            "activation": activation_info,
            "has_cookie": config.has_cookie(),
            "timestamp": chrono::Utc::now().to_rfc3339()
        }))
    }

    /// 处理带ID的闲鱼登录流程（用于自动添加账号）
    pub async fn handle_goldfish_login_with_id(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        info!(
            "GoldfishBusiness::handle_goldfish_login_with_id - 开始带ID的闲鱼登录流程: login_id={}",
            login_id
        );

        // 平台检测：Windows 使用专用流程
        let is_windows = self.is_windows_platform();
        println!("🔍 平台检测结果: Windows = {}", is_windows);

        if is_windows {
            println!("🪟 使用 Windows 专用登录流程");
            return self
                .handle_goldfish_login_with_id_windows(login_id, description)
                .await;
        }

        // macOS/Linux 使用原有流程
        println!("🍎 使用标准登录流程 (macOS/Linux)");
        self.handle_goldfish_login_with_id_standard(login_id, description)
            .await
    }

    /// Windows 专用：处理带ID的闲鱼登录流程
    async fn handle_goldfish_login_with_id_windows(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        println!(
            "🪟 Windows 专用：开始带ID的闲鱼登录流程: login_id={}",
            login_id
        );

        // 1. 保存描述用于窗口标题
        let window_title = description.as_deref().unwrap_or("新账号").to_string();
        println!("📝 窗口标题: {}", window_title);

        // 2. 通过账号服务创建 Windows 专用登录会话
        let windows_service = self
            .account_service
            .get_windows_service()
            .ok_or("Windows 专用账号服务未初始化")?;
        println!("✅ Windows 专用账号服务已获取");

        let session_id = windows_service
            .create_windows_login_session(description)
            .await?;
        println!("✅ Windows 登录会话已创建: {}", session_id);

        // 3. 获取 Windows 会话信息
        let session = windows_service
            .get_windows_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取 Windows 登录会话".to_string())?;
        println!("✅ Windows 会话信息已获取: {:?}", session.data_directory);

        // 4. 使用 WindowFactory 创建登录窗口
        let login_url = "https://www.goofish.com/search?q=相机";
        let window_label = format!("goofish_login_windows_{}", session_id);

        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let params = WindowParams::new()
            .with_title(&format!(
                "闲鱼登录 - {} - 请完成登录后关闭窗口",
                window_title
            ))
            .with_config(WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let session_id_clone = session_id_clone.clone();
                let window = window.clone();

                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🪟 Windows 登录窗口关闭请求，开始处理Cookie提取");

                        // 同步提取Cookie，避免竞态条件（统一处理逻辑）
                        let cookies_result = window.cookies();
                        match cookies_result {
                            Ok(cookies) => {
                                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                                // 保存所有Cookie
                                let all_cookies: Vec<_> = cookies
                                    .into_iter()
                                    .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                    .collect();

                                if !all_cookies.is_empty() {
                                    let cookie_string = all_cookies.join("; ");
                                    println!("🔄 开始处理登录会话完成");

                                    // 异步处理登录会话完成，但不阻止窗口关闭
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        // 根据平台选择处理方式
                                        if cfg!(target_os = "windows") {
                                            // Windows平台使用专用服务
                                            if let Some(windows_service) = self_clone_async.account_service.get_windows_service() {
                                                if let Err(e) = windows_service
                                                    .handle_windows_login_session_completed_with_cookie(&session_id_async, cookie_string)
                                                    .await
                                                {
                                                    println!("❌ Windows 处理登录会话完成失败: {}", e);
                                                }
                                            }
                                        } else {
                                            // 其他平台使用标准处理
                                            if let Err(e) = self_clone_async
                                                .account_service
                                                .handle_login_session_completed_with_cookie(&session_id_async, cookie_string)
                                                .await
                                            {
                                                println!("❌ 处理登录会话完成失败: {}", e);
                                            }
                                        }
                                    });
                                } else {
                                    println!("⚠️ 未找到任何Cookie，清理会话");
                                    // 异步清理会话
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        if cfg!(target_os = "windows") {
                                            if let Some(windows_service) = self_clone_async.account_service.get_windows_service() {
                                                if let Err(e) = windows_service.cleanup_windows_login_session(&session_id_async).await {
                                                    println!("❌ 清理Windows登录会话失败: {}", e);
                                                }
                                            }
                                        } else {
                                            if let Err(e) = self_clone_async.account_service.cleanup_login_session(&session_id_async).await {
                                                println!("❌ 清理登录会话失败: {}", e);
                                            }
                                        }
                                    });
                                }
                            }
                            Err(e) => {
                                println!("⚠️ Cookie提取失败: {}，清理会话", e);
                                // 异步清理会话
                                let self_clone_async = self_clone.clone();
                                let session_id_async = session_id_clone.clone();
                                tauri::async_runtime::spawn(async move {
                                    if cfg!(target_os = "windows") {
                                        if let Some(windows_service) = self_clone_async.account_service.get_windows_service() {
                                            if let Err(e) = windows_service.cleanup_windows_login_session(&session_id_async).await {
                                                println!("❌ 清理Windows登录会话失败: {}", e);
                                            }
                                        }
                                    } else {
                                        if let Err(e) = self_clone_async.account_service.cleanup_login_session(&session_id_async).await {
                                            println!("❌ 清理登录会话失败: {}", e);
                                        }
                                    }
                                });
                            }
                        }
                    }
                    _ => {}
                }
            });

        // 提取会话目录作为存储隔离标识
        let identity = session
            .data_directory
            .file_name()
            .and_then(|name| name.to_str());

        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            &window_label,
            login_url,
            Some(params),
            identity,
        )?;

        println!("✅ Windows 专用登录窗口已打开: {}", window.label());

        Ok(())
    }

    /// 标准平台：处理带ID的闲鱼登录流程（原有逻辑）
    async fn handle_goldfish_login_with_id_standard(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        println!("🍎 标准平台：开始带ID的闲鱼登录流程: login_id={}", login_id);

        // 1. 保存描述用于窗口标题
        let window_title = description.as_deref().unwrap_or("新账号").to_string();

        // 2. 创建登录会话
        let session_id = self
            .account_service
            .create_login_session(description)
            .await?;

        // 3. 获取会话信息
        let session = self
            .account_service
            .get_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取登录会话".to_string())?;

        // 4. 使用 WindowFactory 创建登录窗口
        let login_url = "https://www.goofish.com/search?q=相机";
        let window_label = format!("goofish_login_{}", session_id);

        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let params = WindowParams::new()
            .with_title(&format!(
                "闲鱼登录 - {} - 请完成登录后关闭窗口",
                window_title
            ))
            .with_config(WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let session_id_clone = session_id_clone.clone();
                let window = window.clone();

                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 登录会话窗口关闭请求，开始处理Cookie提取");

                        // 同步提取Cookie，避免竞态条件
                        let cookies_result = window.cookies();
                        match cookies_result {
                            Ok(cookies) => {
                                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                                // 保存所有Cookie
                                let all_cookies: Vec<_> = cookies
                                    .into_iter()
                                    .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                    .collect();

                                if !all_cookies.is_empty() {
                                    let cookie_string = all_cookies.join("; ");
                                    println!("🔄 开始处理登录会话完成");

                                    // 异步处理登录会话完成，但不阻止窗口关闭
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        if let Err(e) = self_clone_async
                                            .account_service
                                            .handle_login_session_completed_with_cookie(
                                                &session_id_async,
                                                cookie_string,
                                            )
                                            .await
                                        {
                                            println!("❌ 处理登录会话完成失败: {}", e);
                                        }
                                    });
                                } else {
                                    println!("⚠️ 未找到任何Cookie，清理会话");
                                    // 异步清理会话
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        if let Err(e) = self_clone_async
                                            .account_service
                                            .cleanup_login_session(&session_id_async)
                                            .await
                                        {
                                            println!("❌ 清理登录会话失败: {}", e);
                                        }
                                    });
                                }
                            }
                            Err(e) => {
                                println!("⚠️ Cookie提取失败: {}，清理会话", e);
                                // 异步清理会话
                                let self_clone_async = self_clone.clone();
                                let session_id_async = session_id_clone.clone();
                                tauri::async_runtime::spawn(async move {
                                    if let Err(e) = self_clone_async
                                        .account_service
                                        .cleanup_login_session(&session_id_async)
                                        .await
                                    {
                                        println!("❌ 清理登录会话失败: {}", e);
                                    }
                                });
                            }
                        }
                    }
                    _ => {}
                }
            });

        // 使用统一的会话ID作为存储隔离标识，而不是平台特定的目录名
        let identity = Some(session_id.as_str());

        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            &window_label,
            login_url,
            Some(params),
            identity,
        )?;

        // 注意：identifier已经在create_login_session时生成并保存了

        info!(
            "GoldfishBusiness::handle_goldfish_login_with_id - 已打开登录窗口: {} (会话ID: {})",
            window.label(),
            session_id
        );

        // 登录会话窗口事件处理现在直接在窗口创建时处理

        Ok(())
    }

    /// 更新指定账号的Cookie
    pub async fn update_account_cookie(&self, account_id: &str) -> Result<(), String> {
        info!(
            "GoldfishBusiness::update_account_cookie - 开始更新账号Cookie: {}",
            account_id
        );

        // 1. 获取账号信息
        let account = self
            .account_service
            .get_account(account_id)
            .await
            .ok_or_else(|| format!("账号不存在: {}", account_id))?;

        // 2. 重用或创建登录会话
        let session_id = self
            .account_service
            .reuse_login_session_for_account(account_id)
            .await?;

        // 3. 获取会话信息
        let session = self
            .account_service
            .get_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取登录会话".to_string())?;

        // 4. 使用 WindowFactory 创建更新Cookie窗口
        let login_url = "https://www.goofish.com/search?q=相机";
        let window_label = format!("goofish_update_{}", session_id);

        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let account_id_clone = account_id.to_string();
        let params = WindowParams::new()
            .with_title(&format!(
                "更新Cookie - {} - 请完成登录后关闭窗口",
                account.name
            ))
            .with_config(WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let session_id_clone = session_id_clone.clone();
                let account_id_clone = account_id_clone.clone();
                let window = window.clone();

                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 更新Cookie窗口关闭请求，开始处理Cookie提取");

                        // 同步提取Cookie，避免竞态条件
                        let cookies_result = window.cookies();
                        match cookies_result {
                            Ok(cookies) => {
                                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                                // 保存所有Cookie
                                let all_cookies: Vec<_> = cookies
                                    .into_iter()
                                    .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                                    .collect();

                                if !all_cookies.is_empty() {
                                    let cookie_string = all_cookies.join("; ");
                                    println!("🔄 开始更新账号Cookie");

                                    // 异步更新账号Cookie，但不阻止窗口关闭
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    let account_id_async = account_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        if let Err(e) = self_clone_async
                                            .account_service
                                            .update_account_cookie_with_session(
                                                &account_id_async,
                                                &session_id_async,
                                                cookie_string,
                                            )
                                            .await
                                        {
                                            println!("❌ 更新账号Cookie失败: {}", e);
                                        } else {
                                            println!("✅ 更新账号Cookie成功");
                                        }
                                    });
                                } else {
                                    println!("⚠️ 未找到Cookie，但保留会话以便下次重用");
                                }
                            }
                            Err(e) => {
                                println!("⚠️ Cookie提取失败: {}，但保留会话以便下次重用", e);
                            }
                        }
                    }
                    _ => {}
                }
            });

        // 使用统一的会话ID作为存储隔离标识，而不是平台特定的目录名
        let identity = Some(session_id.as_str());

        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            &window_label,
            login_url,
            Some(params),
            identity,
        )?;

        // 注意：identifier已经在会话创建/重用时生成并保存了

        info!(
            "GoldfishBusiness::update_account_cookie - 已打开更新Cookie窗口: {} (会话ID: {})",
            window.label(),
            session_id
        );

        // 更新Cookie窗口事件处理现在直接在窗口创建时处理

        Ok(())
    }

    /// 检测是否为 Windows 平台
    fn is_windows_platform(&self) -> bool {
        cfg!(target_os = "windows")
    }

    /// 处理账号验证请求（打开验证窗口）
    pub async fn handle_account_verification(&self, account_id: &str) -> Result<(), String> {
        println!("🔐 处理账号验证请求: account_id={}", account_id);

        info!(
            "GoldfishBusiness::handle_account_verification - 开始处理账号验证: account_id={}",
            account_id
        );

        // 获取账号信息
        let account = {
            let config = self.account_service.get_config().await;
            config
                .accounts
                .iter()
                .find(|a| a.id == account_id)
                .cloned()
                .ok_or_else(|| format!("账号不存在: {}", account_id))?
        };

        // 平台检测：Windows 使用专用流程
        if self.is_windows_platform() {
            return self
                .handle_account_verification_windows(account_id, &account)
                .await;
        }

        // macOS/Linux 使用标准流程
        self.handle_account_verification_standard(account_id, &account)
            .await
    }

    /// Windows 专用：处理账号验证
    async fn handle_account_verification_windows(
        &self,
        account_id: &str,
        account: &crate::models::account::Account,
    ) -> Result<(), String> {
        println!("🪟 Windows 专用：处理账号验证: account_id={}", account_id);

        let windows_service = self
            .account_service
            .get_windows_service()
            .ok_or("Windows 专用账号服务未初始化")?;

        // 重用或创建 Windows 专用登录会话
        let session_id = windows_service
            .reuse_windows_login_session_for_account(account)
            .await?;

        // 获取 Windows 会话信息
        let session = windows_service
            .get_windows_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取 Windows 登录会话".to_string())?;

        // 使用 WindowFactory 创建验证窗口
        let verification_url = "https://www.goofish.com/search?q=相机";
        let window_label = format!("goofish_verification_windows_{}", session_id);

        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let account_id_clone = account_id.to_string();
        let params = WindowParams::new()
            .with_title(&format!(
                "账号验证 - {} - 请完成验证后关闭窗口",
                account.name
            ))
            .with_config(WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let session_id_clone = session_id_clone.clone();
                let account_id_clone = account_id_clone.clone();
                let window = window.clone();

                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🪟 Windows 验证窗口关闭请求，开始处理Cookie提取");

                        // 同步提取Cookie，避免竞态条件（统一处理逻辑）
                        let cookies_result = window.cookies();
                        match cookies_result {
                            Ok(cookies) => {
                                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                                // 查找闲鱼相关的Cookie
                                let mut goofish_cookies = Vec::new();
                                for cookie in cookies {
                                    if let Some(domain) = cookie.domain() {
                                        if domain.contains("goofish.com")
                                            || domain.contains("taobao.com")
                                        {
                                            goofish_cookies.push(cookie);
                                        }
                                    }
                                }

                                if !goofish_cookies.is_empty() {
                                    // 构建Cookie字符串
                                    let cookie_string = goofish_cookies
                                        .iter()
                                        .map(|c| format!("{}={}", c.name(), c.value()))
                                        .collect::<Vec<_>>()
                                        .join("; ");

                                    println!("🔄 Windows验证窗口提取到闲鱼Cookie，开始更新账号");

                                    // 异步处理验证完成，但不阻止窗口关闭
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    let account_id_async = account_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        // 使用 Windows 专用服务处理Cookie
                                        if let Some(windows_service) =
                                            self_clone_async.account_service.get_windows_service()
                                        {
                                            match windows_service
                                                .handle_windows_login_session_completed_with_cookie(
                                                    &session_id_async,
                                                    cookie_string,
                                                )
                                                .await
                                            {
                                                Ok((account, _)) => {
                                                    println!(
                                                        "✅ Windows 验证Cookie处理成功: {}",
                                                        account.name
                                                    );

                                                    // 处理验证完成
                                                    if let Err(e) = self_clone_async
                                                        .handle_account_verification_completed(
                                                            &account_id_async,
                                                        )
                                                        .await
                                                    {
                                                        println!("⚠️ 处理账号验证完成失败: {}", e);
                                                    }
                                                }
                                                Err(e) => {
                                                    println!(
                                                        "❌ Windows 验证Cookie处理失败: {}",
                                                        e
                                                    );
                                                }
                                            }
                                        }
                                    });
                                } else {
                                    println!("⚠️ 未提取到有效的闲鱼Cookie");
                                }
                            }
                            Err(e) => {
                                println!("❌ 提取Cookie失败: {}", e);
                            }
                        }
                    }
                    _ => {}
                }
            });

        // 使用统一的会话ID作为存储隔离标识，而不是平台特定的目录名
        let identity = Some(session_id.as_str());

        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            &window_label,
            verification_url,
            Some(params),
            identity,
        )?;

        println!("✅ Windows 专用验证窗口已打开: {}", window.label());

        Ok(())
    }

    /// 标准平台：处理账号验证
    async fn handle_account_verification_standard(
        &self,
        account_id: &str,
        account: &crate::models::account::Account,
    ) -> Result<(), String> {
        println!("🍎 标准平台：处理账号验证: account_id={}", account_id);

        // 重用现有会话ID更新账号Cookie
        let session_id = self
            .account_service
            .reuse_login_session_for_account(account_id)
            .await?;

        // 获取会话信息
        let session = self
            .account_service
            .get_login_session(&session_id)
            .await
            .ok_or_else(|| "无法获取登录会话".to_string())?;

        // 使用 WindowFactory 创建验证窗口
        let verification_url = "https://www.goofish.com/search?q=相机";
        let window_label = format!("goofish_verification_{}", session_id);

        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let account_id_clone = account_id.to_string();
        let params = WindowParams::new()
            .with_title(&format!(
                "账号验证 - {} - 请完成验证后关闭窗口",
                account.name
            ))
            .with_config(WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let session_id_clone = session_id_clone.clone();
                let account_id_clone = account_id_clone.clone();
                let window = window.clone();
                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 验证窗口关闭请求，开始处理Cookie提取");

                        // 同步提取Cookie，避免竞态条件（统一处理逻辑）
                        let cookies_result = window.cookies();
                        match cookies_result {
                            Ok(cookies) => {
                                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                                // 查找闲鱼相关的Cookie
                                let mut goofish_cookies = Vec::new();
                                for cookie in cookies {
                                    if let Some(domain) = cookie.domain() {
                                        if domain.contains("goofish.com")
                                            || domain.contains("taobao.com")
                                        {
                                            goofish_cookies.push(cookie);
                                        }
                                    }
                                }

                                if !goofish_cookies.is_empty() {
                                    // 构建Cookie字符串
                                    let cookie_string = goofish_cookies
                                        .iter()
                                        .map(|c| format!("{}={}", c.name(), c.value()))
                                        .collect::<Vec<_>>()
                                        .join("; ");

                                    println!("🔄 验证窗口提取到闲鱼Cookie，开始更新账号");

                                    // 异步处理验证完成，但不阻止窗口关闭
                                    let self_clone_async = self_clone.clone();
                                    let session_id_async = session_id_clone.clone();
                                    let account_id_async = account_id_clone.clone();
                                    tauri::async_runtime::spawn(async move {
                                        // 处理登录会话完成
                                        match self_clone_async
                                            .account_service
                                            .handle_login_session_completed_with_cookie(
                                                &session_id_async,
                                                cookie_string,
                                            )
                                            .await
                                        {
                                            Ok(()) => {
                                                println!("✅ 验证Cookie处理成功");

                                                // 处理验证完成
                                                if let Err(e) = self_clone_async
                                                    .handle_account_verification_completed(
                                                        &account_id_async,
                                                    )
                                                    .await
                                                {
                                                    println!("⚠️ 处理账号验证完成失败: {}", e);
                                                }
                                            }
                                            Err(e) => {
                                                println!("❌ 验证Cookie处理失败: {}", e);
                                            }
                                        }
                                    });
                                } else {
                                    println!("⚠️ 未提取到有效的闲鱼Cookie");
                                }
                            }
                            Err(e) => {
                                println!("❌ 提取Cookie失败: {}", e);
                            }
                        }
                    }
                    _ => {}
                }
            });

        // 使用统一的会话ID作为存储隔离标识，而不是平台特定的目录名
        let identity = Some(session_id.as_str());

        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            &window_label,
            verification_url,
            Some(params),
            identity,
        )?;

        println!("✅ 标准平台验证窗口已打开: {}", window.label());

        // 验证窗口事件处理现在直接在窗口创建时处理

        Ok(())
    }

    /// 处理账号验证完成（现在验证窗口会自动更新Cookie）
    pub async fn handle_account_verification_completed(
        &self,
        account_id: &str,
    ) -> Result<(), String> {
        println!("✅ 处理账号验证完成: account_id={}", account_id);

        info!(
            "GoldfishBusiness::handle_account_verification_completed - 账号验证完成: account_id={}",
            account_id
        );

        // 由于验证窗口已经自动更新了Cookie，这里直接验证Cookie有效性
        let is_valid = self
            .account_service
            .validate_account_cookie(account_id)
            .await?;

        if is_valid {
            // Cookie有效，恢复监控
            self.monitor_service
                .resume_account_after_verification(account_id)
                .await?;

            info!("GoldfishBusiness::handle_account_verification_completed - 账号 {} Cookie验证成功，已恢复监控", account_id);

            println!("✅ 账号 {} 验证成功，监控已恢复", account_id);
        } else {
            log::warn!("GoldfishBusiness::handle_account_verification_completed - 账号 {} Cookie验证失败，保持暂停状态", account_id);

            println!("❌ 账号 {} Cookie验证失败，保持暂停状态", account_id);
        }

        Ok(())
    }

    /// Windows 专用：处理验证窗口关闭事件
    async fn handle_windows_verification_window_close(
        &self,
        window: &tauri::WebviewWindow,
        session_id: &str,
        account_id: &str,
    ) -> Result<(), String> {
        println!(
            "🪟 处理 Windows 验证窗口关闭，开始提取Cookie: 会话ID {} 账号ID {}",
            session_id, account_id
        );

        // 直接获取 Cookie
        let cookies_result = window.cookies();
        match cookies_result {
            Ok(cookies) => {
                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                // 保存所有Cookie
                let all_cookies: Vec<_> = cookies
                    .into_iter()
                    .map(|cookie| format!("{}={}", cookie.name(), cookie.value()))
                    .collect();

                if !all_cookies.is_empty() {
                    let cookie_string = all_cookies.join("; ");
                    println!("🔄 开始处理 Windows 验证Cookie更新");

                    // 使用 Windows 专用服务处理Cookie
                    if let Some(windows_service) = self.account_service.get_windows_service() {
                        match windows_service
                            .handle_windows_login_session_completed_with_cookie(
                                session_id,
                                cookie_string,
                            )
                            .await
                        {
                            Ok((account, _)) => {
                                println!("✅ Windows 验证Cookie处理成功: {}", account.name);

                                // 账号已经通过 Windows 服务更新，无需额外更新
                                println!("✅ Windows 验证账号已更新: {}", account.name);

                                // 处理验证完成
                                if let Err(e) =
                                    self.handle_account_verification_completed(account_id).await
                                {
                                    println!("⚠️ 处理账号验证完成失败: {}", e);
                                }
                            }
                            Err(e) => {
                                println!("❌ Windows 验证Cookie处理失败: {}", e);
                            }
                        }
                    }
                } else {
                    println!("⚠️ 未提取到有效的Cookie");
                }
            }
            Err(e) => {
                println!("❌ 提取Cookie失败: {}", e);
            }
        }

        // 清理 Windows 会话
        if let Some(windows_service) = self.account_service.get_windows_service() {
            if let Err(e) = windows_service
                .cleanup_windows_login_session(session_id)
                .await
            {
                println!("⚠️ 清理 Windows 验证会话失败: {}", e);
            }
        }

        Ok(())
    }
    /// 标准平台：处理验证窗口关闭事件（新方法）
    async fn handle_verification_window_close(
        &self,
        window: &tauri::WebviewWindow,
        session_id: &str,
        account_id: &str,
    ) -> Result<(), String> {
        println!(
            "🔄 处理验证窗口关闭，开始提取Cookie: 会话ID {} 账号ID {}",
            session_id, account_id
        );

        // 直接获取 Cookie
        let cookies_result = window.cookies();
        match cookies_result {
            Ok(cookies) => {
                println!("🍪 成功提取了 {} 个Cookie", cookies.len());

                // 查找闲鱼相关的Cookie
                let mut goofish_cookies = Vec::new();
                for cookie in cookies {
                    if let Some(domain) = cookie.domain() {
                        if domain.contains("goofish.com") || domain.contains("taobao.com") {
                            goofish_cookies.push(cookie);
                        }
                    }
                }

                if !goofish_cookies.is_empty() {
                    // 构建Cookie字符串
                    let cookie_string = goofish_cookies
                        .iter()
                        .map(|c| format!("{}={}", c.name(), c.value()))
                        .collect::<Vec<_>>()
                        .join("; ");

                    println!("🔄 验证窗口提取到闲鱼Cookie，开始更新账号");

                    // 处理登录会话完成
                    match self
                        .account_service
                        .handle_login_session_completed_with_cookie(session_id, cookie_string)
                        .await
                    {
                        Ok(()) => {
                            println!("✅ 验证Cookie处理成功");

                            // 处理验证完成
                            if let Err(e) =
                                self.handle_account_verification_completed(account_id).await
                            {
                                println!("⚠️ 处理账号验证完成失败: {}", e);
                            }
                        }
                        Err(e) => {
                            println!("❌ 验证Cookie处理失败: {}", e);
                        }
                    }
                } else {
                    println!("⚠️ 未提取到有效的闲鱼Cookie");
                }
            }
            Err(e) => {
                println!("❌ 提取Cookie失败: {}", e);
            }
        }

        // 清理会话
        if let Err(e) = self.account_service.cleanup_login_session(session_id).await {
            println!("⚠️ 清理验证会话失败: {}", e);
        }

        Ok(())
    }
}
